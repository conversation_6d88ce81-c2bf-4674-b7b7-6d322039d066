import dotenv from 'dotenv';
import { Pinecone } from '@pinecone-database/pinecone';
import { GoogleGenerativeAIEmbeddings } from '@langchain/google-genai';
import { LangChainRAGService } from '../src/rag-langchain';

// Load environment variables
dotenv.config();

/**
 * Migration script to transition from text-embedding-004 (768 dimensions) 
 * to gemini-embedding-001 (3072 dimensions)
 * 
 * This script will:
 * 1. Create a new Pinecone index with 3072 dimensions
 * 2. Fetch all existing data from the old index
 * 3. Re-embed the text content using gemini-embedding-001
 * 4. Upload to the new index
 * 5. Provide instructions for switching over
 */
class EmbeddingMigrationManager {
  private pinecone: Pinecone;
  private oldEmbeddings: GoogleGenerativeAIEmbeddings;
  private newEmbeddings: GoogleGenerativeAIEmbeddings;
  private oldIndex: any;
  private newIndex: any;
  private oldIndexName: string;
  private newIndexName: string;

  constructor() {
    this.pinecone = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY!,
    });

    // Old embedding model (768 dimensions)
    this.oldEmbeddings = new GoogleGenerativeAIEmbeddings({
      apiKey: process.env.GOOGLE_AI_API_KEY!,
      model: "text-embedding-004",
    });

    // New embedding model (3072 dimensions)
    this.newEmbeddings = new GoogleGenerativeAIEmbeddings({
      apiKey: process.env.GOOGLE_AI_API_KEY!,
      model: "models/gemini-embedding-001",
    });

    this.oldIndexName = process.env.PINECONE_INDEX!;
    this.newIndexName = `${this.oldIndexName}-gemini-001`;
    
    this.oldIndex = this.pinecone.index(this.oldIndexName);
    this.newIndex = this.pinecone.index(this.newIndexName);
  }

  /**
   * Step 1: Create new index with 3072 dimensions
   */
  async createNewIndex(): Promise<void> {
    try {
      console.log('🔧 Creating new Pinecone index with 3072 dimensions...');
      
      // Check if new index already exists
      const existingIndexes = await this.pinecone.listIndexes();
      const indexExists = existingIndexes.indexes?.some(index => index.name === this.newIndexName);
      
      if (indexExists) {
        console.log(`✅ Index "${this.newIndexName}" already exists`);
        return;
      }

      await this.pinecone.createIndex({
        name: this.newIndexName,
        dimension: 3072, // gemini-embedding-001 dimensions
        metric: 'cosine',
        spec: {
          serverless: {
            cloud: 'aws',
            region: 'us-east-1'
          }
        }
      });

      console.log(`✅ Created new index: ${this.newIndexName}`);
      
      // Wait for index to be ready
      console.log('⏳ Waiting for index to be ready...');
      await this.waitForIndexReady(this.newIndexName);
      
    } catch (error) {
      console.error('❌ Error creating new index:', error);
      throw error;
    }
  }

  /**
   * Wait for index to be ready
   */
  private async waitForIndexReady(indexName: string): Promise<void> {
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      try {
        const indexStats = await this.pinecone.index(indexName).describeIndexStats();
        if (indexStats) {
          console.log('✅ Index is ready!');
          return;
        }
      } catch (error) {
        // Index not ready yet
      }
      
      attempts++;
      console.log(`⏳ Waiting for index... (${attempts}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    throw new Error('Index did not become ready within expected time');
  }

  /**
   * Step 2: Fetch all existing vectors from old index
   */
  async fetchExistingData(): Promise<any[]> {
    try {
      console.log('📥 Fetching existing data from old index...');
      
      // Get index stats to understand the data
      const stats = await this.oldIndex.describeIndexStats();
      console.log(`📊 Old index contains ${stats.totalRecordCount || 0} vectors`);

      if ((stats.totalRecordCount || 0) === 0) {
        console.log('⚠️ No data found in old index');
        return [];
      }

      // Fetch all vectors (this is a simplified approach)
      // In production, you might want to implement pagination
      const queryResponse = await this.oldIndex.query({
        vector: new Array(768).fill(0), // Dummy vector for fetching
        topK: 10000, // Adjust based on your data size
        includeMetadata: true,
        includeValues: false // We only need metadata, will re-embed
      });

      console.log(`📥 Fetched ${queryResponse.matches.length} documents`);
      return queryResponse.matches;
      
    } catch (error) {
      console.error('❌ Error fetching existing data:', error);
      throw error;
    }
  }

  /**
   * Step 3: Re-embed and migrate data
   */
  async migrateData(existingData: any[]): Promise<void> {
    try {
      console.log('🔄 Re-embedding and migrating data...');
      
      const batchSize = 10; // Process in small batches to avoid rate limits
      const batches = [];
      
      for (let i = 0; i < existingData.length; i += batchSize) {
        batches.push(existingData.slice(i, i + batchSize));
      }
      
      console.log(`📦 Processing ${batches.length} batches of ${batchSize} documents each`);
      
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`🔄 Processing batch ${batchIndex + 1}/${batches.length}`);
        
        const vectors = [];
        
        for (const item of batch) {
          if (!item.metadata?.text) {
            console.log(`⚠️ Skipping item ${item.id} - no text content`);
            continue;
          }
          
          try {
            // Generate new embedding with gemini-embedding-001
            const newEmbedding = await this.newEmbeddings.embedQuery(item.metadata.text);
            
            vectors.push({
              id: item.id,
              values: newEmbedding,
              metadata: item.metadata
            });
            
            console.log(`  ✓ Re-embedded: ${item.id}`);
            
            // Small delay to respect rate limits
            await new Promise(resolve => setTimeout(resolve, 100));
            
          } catch (error) {
            console.error(`❌ Error re-embedding ${item.id}:`, error);
          }
        }
        
        // Upload batch to new index
        if (vectors.length > 0) {
          await this.newIndex.upsert(vectors);
          console.log(`✅ Uploaded batch ${batchIndex + 1} (${vectors.length} vectors)`);
        }
        
        // Longer delay between batches
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      console.log('✅ Data migration completed!');
      
    } catch (error) {
      console.error('❌ Error migrating data:', error);
      throw error;
    }
  }

  /**
   * Step 4: Verify migration
   */
  async verifyMigration(): Promise<void> {
    try {
      console.log('🔍 Verifying migration...');
      
      const oldStats = await this.oldIndex.describeIndexStats();
      const newStats = await this.newIndex.describeIndexStats();

      console.log(`📊 Old index: ${oldStats.totalRecordCount || 0} vectors`);
      console.log(`📊 New index: ${newStats.totalRecordCount || 0} vectors`);

      if ((newStats.totalRecordCount || 0) > 0) {
        console.log('✅ Migration verification successful!');
        
        // Test a sample query
        console.log('🧪 Testing sample query...');
        const testEmbedding = await this.newEmbeddings.embedQuery("test query");
        const testResult = await this.newIndex.query({
          vector: testEmbedding,
          topK: 1,
          includeMetadata: true
        });
        
        if (testResult.matches.length > 0) {
          console.log('✅ Sample query successful!');
          console.log(`   Score: ${testResult.matches[0].score}`);
          console.log(`   Content preview: ${testResult.matches[0].metadata?.text?.substring(0, 100)}...`);
        }
      } else {
        console.log('⚠️ New index appears to be empty');
      }
      
    } catch (error) {
      console.error('❌ Error verifying migration:', error);
      throw error;
    }
  }

  /**
   * Complete migration process
   */
  async runMigration(): Promise<void> {
    try {
      console.log('🚀 Starting migration to gemini-embedding-001...');
      console.log(`📋 Old index: ${this.oldIndexName} (768 dimensions)`);
      console.log(`📋 New index: ${this.newIndexName} (3072 dimensions)`);
      
      // Step 1: Create new index
      await this.createNewIndex();
      
      // Step 2: Fetch existing data
      const existingData = await this.fetchExistingData();
      
      if (existingData.length === 0) {
        console.log('⚠️ No data to migrate. You can start fresh with the new index.');
        return;
      }
      
      // Step 3: Migrate data
      await this.migrateData(existingData);
      
      // Step 4: Verify migration
      await this.verifyMigration();
      
      console.log('\n🎉 Migration completed successfully!');
      console.log('\n📝 Next steps:');
      console.log(`1. Update your .env file: PINECONE_INDEX=${this.newIndexName}`);
      console.log('2. Restart your application');
      console.log('3. Test the chatbot with some queries');
      console.log('4. Once confirmed working, you can delete the old index');
      console.log(`\n⚠️ Keep both indexes for now until you confirm everything works!`);
      
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Show current status
   */
  async showStatus(): Promise<void> {
    try {
      console.log('📊 Current Migration Status:');
      
      const indexes = await this.pinecone.listIndexes();
      const indexNames = indexes.indexes?.map(idx => idx.name) || [];
      
      console.log(`\n📋 Available indexes:`);
      for (const name of indexNames) {
        const stats = await this.pinecone.index(name).describeIndexStats();
        const isOld = name === this.oldIndexName;
        const isNew = name === this.newIndexName;
        
        console.log(`  ${isOld ? '🔴' : isNew ? '🟢' : '⚪'} ${name}: ${stats.totalRecordCount || 0} vectors`);
      }
      
      console.log(`\n🔴 Old index (768 dim): ${this.oldIndexName}`);
      console.log(`🟢 New index (3072 dim): ${this.newIndexName}`);
      
    } catch (error) {
      console.error('❌ Error checking status:', error);
    }
  }
}

// Command line interface
async function main() {
  const args = process.argv.slice(2);
  
  if (!process.env.GOOGLE_AI_API_KEY || !process.env.PINECONE_API_KEY || !process.env.PINECONE_INDEX) {
    console.error('❌ Missing required environment variables');
    console.error('Required: GOOGLE_AI_API_KEY, PINECONE_API_KEY, PINECONE_INDEX');
    process.exit(1);
  }
  
  const migrator = new EmbeddingMigrationManager();
  
  if (args.includes('--status')) {
    await migrator.showStatus();
  } else if (args.includes('--help')) {
    console.log(`
🔄 Gemini Embedding Migration Tool

Usage:
  npm run migrate:embedding           # Run full migration
  npm run migrate:embedding --status  # Check current status
  npm run migrate:embedding --help    # Show this help

This tool migrates from text-embedding-004 (768 dim) to gemini-embedding-001 (3072 dim)
    `);
  } else {
    await migrator.runMigration();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { EmbeddingMigrationManager };
